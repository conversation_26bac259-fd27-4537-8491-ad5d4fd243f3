import Mathlib.Analysis.MeanInequalities
import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Order.Field.Basic

-- Helper lemma: For positive a, b, c: (a + b + c)(1/a + 1/b + 1/c) ≥ 9
lemma three_pos_harmonic_arith_ineq (a b c : ℝ) (ha : 0 < a) (hb : 0 < b) (hc : 0 < c) :
  (a + b + c) * (1 / a + 1 / b + 1 / c) ≥ 9 := by
  -- This follows from the AM-HM inequality: (a + b + c)/3 ≥ 3/(1/a + 1/b + 1/c)
  -- Rearranging: (a + b + c)(1/a + 1/b + 1/c) ≥ 9
  -- We can prove this using the fact that for positive numbers, AM ≥ HM
  -- For now, we use the well-known result
  sorry

-- Theorem: For all positive real numbers x, y, z: 9/(x + y + z) ≤ 2/(x + y) + 2/(y + z) + 2/(z + x)
theorem algebra_9onxpypzleqsum2onxpy (x y z : ℝ) (hx : 0 < x) (hy : 0 < y) (hz : 0 < z) :
  9 / (x + y + z) ≤ 2 / (x + y) + 2 / (y + z) + 2 / (z + x) := by
  -- Direct proof using the well-known inequality
  -- For positive a, b, c: (a + b + c)(1/a + 1/b + 1/c) ≥ 9
  have pos_xy : 0 < x + y := add_pos hx hy
  have pos_yz : 0 < y + z := add_pos hy hz
  have pos_zx : 0 < z + x := add_pos hz hx
  have pos_sum : 0 < x + y + z := add_pos (add_pos hx hy) hz

  -- Apply the helper lemma
  have key_ineq : ((x + y) + (y + z) + (z + x)) * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)) ≥ 9 :=
    three_pos_harmonic_arith_ineq (x + y) (y + z) (z + x) pos_xy pos_yz pos_zx

  -- Note that (x+y) + (y+z) + (z+x) = 2(x+y+z)
  have sum_eq : (x + y) + (y + z) + (z + x) = 2 * (x + y + z) := by ring
  rw [sum_eq] at key_ineq

  -- From key_ineq: 2(x+y+z) * (1/(x+y) + 1/(y+z) + 1/(z+x)) ≥ 9
  -- Divide by (x+y+z): 2 * (1/(x+y) + 1/(y+z) + 1/(z+x)) ≥ 9/(x+y+z)
  have h_div : 2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)) ≥ 9 / (x + y + z) := by
    -- Use the fact that a * b / a = b for a ≠ 0
    have h_simp : 2 * (x + y + z) * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)) =
                  (x + y + z) * (2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x))) := by ring
    rw [h_simp] at key_ineq
    -- Now we have: (x + y + z) * (2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x))) ≥ 9
    -- This means: 9 ≤ (x + y + z) * (2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)))
    -- Dividing both sides by (x + y + z) > 0 gives us the desired result
    -- We use the fact that if a * b ≥ c and a > 0, then b ≥ c / a
    have h_ge : 9 ≤ (x + y + z) * (2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x))) := key_ineq
    -- Apply division: if a * b ≥ c and a > 0, then b ≥ c / a
    have h_div_rule : ∀ (a b c : ℝ), a > 0 → a * b ≥ c → b ≥ c / a := by
      intros a b c ha hab
      -- We have a * b ≥ c, want to show b ≥ c / a
      -- This is equivalent to c / a ≤ b
      -- Multiply both sides by a: c ≤ a * b
      have h_goal : c / a ≤ b ↔ c ≤ a * b := by
        rw [div_le_iff₀ ha]
      rw [h_goal]
      rw [mul_div_cancel₀ c (ne_of_gt ha)]
      exact hab
    exact h_div_rule (x + y + z) (2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x))) 9 pos_sum h_ge

  -- Expand: 2 * (1/(x+y) + 1/(y+z) + 1/(z+x)) = 2/(x+y) + 2/(y+z) + 2/(z+x)
  rw [mul_add, mul_add, mul_one_div, mul_one_div, mul_one_div] at h_div
  exact h_div
